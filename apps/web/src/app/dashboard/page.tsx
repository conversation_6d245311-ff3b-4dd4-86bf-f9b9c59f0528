'use client';

import { Loading } from '@/components/ui/loading';
import { apiClient } from '@/lib/api';
import { useAuth, withAuth } from '@/lib/auth';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { Calculator, Home, MapPin, Target, TrendingUp } from 'lucide-react';

function DashboardPage() {
  const { user, logout } = useAuth();

  const { data: propertyStats, isLoading: statsLoading } = useQuery({
    queryKey: ['property-statistics'],
    queryFn: () => apiClient.getPropertyStatistics(),
  });

  const { data: recentProperties, isLoading: propertiesLoading } = useQuery({
    queryKey: ['recent-properties'],
    queryFn: () => apiClient.getProperties({ limit: 6, sortBy: 'createdAt', sortOrder: 'desc' }),
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-primary-600">Revalu</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="/properties" className="text-gray-700 hover:text-primary-600 transition-colors">
                Properties
              </a>
              <a href="/suburbs" className="text-gray-700 hover:text-primary-600 transition-colors">
                Suburbs
              </a>
              <a href="/valuations" className="text-gray-700 hover:text-primary-600 transition-colors">
                Valuations
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              <a href="/tracked-properties" className="text-gray-700 hover:text-primary-600 transition-colors">
                Tracked
              </a>
              <span className="text-gray-700">
                Welcome, {user?.firstName || user?.email}
              </span>
              <button
                onClick={logout}
                className="text-gray-700 hover:text-primary-600 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.firstName || 'User'}!
          </h2>
          <p className="text-gray-600">
            Here's what's happening in the property market today.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Home className="w-6 h-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Properties</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {statsLoading ? <Loading size="sm" /> : formatNumber(propertyStats?.totalProperties || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Property Value</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {statsLoading ? <Loading size="sm" /> : formatCurrency(propertyStats?.averagePrice || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MapPin className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Suburbs Covered</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {statsLoading ? <Loading size="sm" /> : formatNumber(propertyStats?.totalSuburbs || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Calculator className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Valuations</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {statsLoading ? <Loading size="sm" /> : formatNumber(propertyStats?.totalValuations || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Quick Actions</h3>
              <p className="card-description">Get started with these common tasks</p>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-2 gap-4">
                <a
                  href="/properties"
                  className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <Home className="w-8 h-8 text-primary-600 mb-2" />
                  <h4 className="font-medium text-gray-900">Browse Properties</h4>
                  <p className="text-sm text-gray-600">Explore available properties</p>
                </a>

                <a
                  href="/tracked-properties"
                  className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <Target className="w-8 h-8 text-primary-600 mb-2" />
                  <h4 className="font-medium text-gray-900">Tracked Properties</h4>
                  <p className="text-sm text-gray-600">Monitor your tracked properties</p>
                </a>

                <a
                  href="/valuations"
                  className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <Calculator className="w-8 h-8 text-primary-600 mb-2" />
                  <h4 className="font-medium text-gray-900">Get Valuation</h4>
                  <p className="text-sm text-gray-600">Value a property</p>
                </a>

                <a
                  href="/suburbs"
                  className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <MapPin className="w-8 h-8 text-primary-600 mb-2" />
                  <h4 className="font-medium text-gray-900">Explore Suburbs</h4>
                  <p className="text-sm text-gray-600">Research market areas</p>
                </a>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Recent Activity</h3>
              <p className="card-description">Your latest property interactions</p>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Viewed property at 123 Main St</span>
                  <span className="text-xs text-gray-400">2 hours ago</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Requested valuation for Toorak property</span>
                  <span className="text-xs text-gray-400">1 day ago</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Saved search for South Yarra apartments</span>
                  <span className="text-xs text-gray-400">3 days ago</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Properties */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recently Added Properties</h3>
            <p className="card-description">Latest properties in the market</p>
          </div>
          <div className="card-content">
            {propertiesLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recentProperties?.data.slice(0, 3).map((property) => (
                  <div key={property.id} className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors">
                    <div className="h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                      <Home className="w-8 h-8 text-gray-400" />
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">{property.address}</h4>
                    <p className="text-sm text-gray-600 mb-2">
                      {property.bedrooms} bed • {property.bathrooms} bath
                    </p>
                    {property.listingPrice && (
                      <p className="text-lg font-bold text-primary-600">
                        {formatCurrency(property.listingPrice)}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(DashboardPage);
