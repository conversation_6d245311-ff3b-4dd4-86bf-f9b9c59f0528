import { Controller, Post, Get, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from '../auth/decorators/public.decorator';
import { ApiSuccessResponse, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { WebhooksService } from './webhooks.service';
import { 
  PropertyDataWebhookDto, 
  MarketDataWebhookDto, 
  WebhookResponseDto,
  DataSourceStatusDto 
} from './dto/webhook.dto';

@ApiTags('webhooks')
@Controller('webhooks')
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('property-data')
  @Public()
  @ApiOperation({ 
    summary: 'Receive property data from external data pipeline',
    description: 'Webhook endpoint for receiving enriched property data from the external data pipeline. This endpoint processes property valuations, Event-Uplift factors, and intelligence scores.'
  })
  @ApiSuccessResponse(WebhookResponseDto, 'Property data processed successfully')
  @ApiErrorResponse(400, 'Invalid webhook payload')
  async receivePropertyData(
    @Body() dto: PropertyDataWebhookDto,
  ): Promise<WebhookResponseDto> {
    return this.webhooksService.processPropertyData(dto);
  }

  @Post('market-data')
  @Public()
  @ApiOperation({ 
    summary: 'Receive market data from external data pipeline',
    description: 'Webhook endpoint for receiving market trends, infrastructure updates, development applications, and other area-based data.'
  })
  @ApiSuccessResponse(WebhookResponseDto, 'Market data processed successfully')
  @ApiErrorResponse(400, 'Invalid webhook payload')
  async receiveMarketData(
    @Body() dto: MarketDataWebhookDto,
  ): Promise<WebhookResponseDto> {
    return this.webhooksService.processMarketData(dto);
  }

  @Get('data-sources/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: 'Get status of all data sources',
    description: 'Returns the current status, health, and performance metrics of all 18 data sources.'
  })
  @ApiSuccessResponse([DataSourceStatusDto], 'Data source status retrieved successfully')
  async getDataSourceStatus(): Promise<DataSourceStatusDto[]> {
    return this.webhooksService.getDataSourceStatus();
  }
}
