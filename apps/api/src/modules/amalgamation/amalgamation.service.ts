import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@revalu/database';
import { 
  CreateAmalgamationDto, 
  AmalgamationAnalysisDto, 
  AmalgamationResponseDto,
  AmalgamationListDto,
  UpdateAmalgamationDto 
} from './dto/amalgamation.dto';

@Injectable()
export class AmalgamationService {
  constructor(private readonly prisma: PrismaService) {}

  async create(userId: string, dto: CreateAmalgamationDto) {
    // Validate that all properties exist
    const properties = await this.prisma.property.findMany({
      where: {
        id: { in: dto.propertyIds },
      },
      include: {
        suburb: true,
      },
    });

    if (properties.length !== dto.propertyIds.length) {
      throw new BadRequestException('One or more properties not found');
    }

    // Check if properties are in reasonable proximity (same suburb for now)
    const suburbs = [...new Set(properties.map(p => p.suburbId))];
    if (suburbs.length > 1) {
      throw new BadRequestException('Properties must be in the same suburb for amalgamation');
    }

    // Calculate initial analysis
    const totalLandSize = properties.reduce((sum, p) => sum + (p.landSize || 0), 0);
    const totalValue = properties.reduce((sum, p) => sum + (p.lastSalePrice || p.suburb.medianPrice || 0), 0);

    // Create amalgamation record
    const amalgamation = await this.prisma.amalgamation.create({
      data: {
        userId,
        name: dto.name,
        propertyIds: dto.propertyIds,
        totalLandSize,
        totalValue,
        developmentPlan: {
          targetDwellings: dto.targetDwellings,
          developmentType: dto.developmentType,
        },
      },
    });

    return amalgamation;
  }

  async findAllByUser(userId: string): Promise<AmalgamationListDto> {
    const amalgamations = await this.prisma.amalgamation.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    return {
      userId,
      amalgamations: amalgamations.map(a => ({
        id: a.id,
        name: a.name,
        propertyCount: a.propertyIds.length,
        totalLandSize: a.totalLandSize || 0,
        estimatedValue: a.totalValue || 0,
        createdAt: a.createdAt,
      })),
    };
  }

  async analyze(dto: AmalgamationAnalysisDto): Promise<AmalgamationResponseDto> {
    const amalgamation = await this.prisma.amalgamation.findUnique({
      where: { id: dto.amalgamationId },
    });

    if (!amalgamation) {
      throw new NotFoundException('Amalgamation not found');
    }

    // Get detailed property information
    const properties = await this.prisma.property.findMany({
      where: {
        id: { in: amalgamation.propertyIds },
      },
      include: {
        suburb: true,
        valuations: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });

    // Perform comprehensive analysis
    const landAnalysis = this.analyzeLand(properties);
    const currentValue = this.analyzeCurrentValue(properties);
    const developmentPotential = this.analyzeDevelopmentPotential(properties, landAnalysis);
    const financialAnalysis = this.analyzeFinancials(properties, developmentPotential);
    const assemblyStrategy = this.createAssemblyStrategy(properties);
    const comparison = this.compareWithIndividualDevelopment(properties, developmentPotential);

    return {
      id: amalgamation.id,
      name: amalgamation.name,
      propertyIds: amalgamation.propertyIds,
      landAnalysis,
      currentValue,
      developmentPotential,
      financialAnalysis,
      assemblyStrategy,
      comparison,
    };
  }

  async update(id: string, userId: string, dto: UpdateAmalgamationDto) {
    const amalgamation = await this.prisma.amalgamation.findFirst({
      where: { id, userId },
    });

    if (!amalgamation) {
      throw new NotFoundException('Amalgamation not found');
    }

    return this.prisma.amalgamation.update({
      where: { id },
      data: {
        ...dto,
        updatedAt: new Date(),
      },
    });
  }

  async delete(id: string, userId: string) {
    const amalgamation = await this.prisma.amalgamation.findFirst({
      where: { id, userId },
    });

    if (!amalgamation) {
      throw new NotFoundException('Amalgamation not found');
    }

    return this.prisma.amalgamation.delete({
      where: { id },
    });
  }

  private analyzeLand(properties: any[]) {
    const totalLandSize = properties.reduce((sum, p) => sum + (p.landSize || 0), 0);
    const avgWidth = 20; // Mock calculation
    const avgDepth = totalLandSize / avgWidth / properties.length;
    const totalFrontage = properties.length * avgWidth;

    return {
      totalLandSize,
      averageWidth: avgWidth,
      averageDepth: avgDepth,
      totalFrontage,
      accessibility: 'Good street access',
      topography: 'Relatively flat',
    };
  }

  private analyzeCurrentValue(properties: any[]) {
    const individualValues = properties.map(p => ({
      propertyId: p.id,
      address: p.address,
      value: p.lastSalePrice || p.suburb.medianPrice || 0,
      landSize: p.landSize || 0,
    }));

    const totalValue = individualValues.reduce((sum, v) => sum + v.value, 0);
    const totalLandSize = individualValues.reduce((sum, v) => sum + v.landSize, 0);
    const averagePerSqm = totalLandSize > 0 ? totalValue / totalLandSize : 0;

    return {
      totalValue,
      averagePerSqm,
      individualValues,
    };
  }

  private analyzeDevelopmentPotential(properties: any[], landAnalysis: any) {
    const totalLandSize = landAnalysis.totalLandSize;
    const maxDwellings = Math.floor(totalLandSize / 300); // Assuming 300sqm per dwelling minimum
    const recommendedDwellings = Math.floor(maxDwellings * 0.8); // Conservative approach
    const developmentScore = this.calculateDevelopmentScore(properties, landAnalysis);

    return {
      maxDwellings,
      recommendedDwellings,
      developmentScore,
      constraints: [
        'Council approval required',
        'Infrastructure capacity',
        'Heritage considerations',
      ],
      opportunities: [
        'Larger development scale',
        'Better design flexibility',
        'Shared infrastructure costs',
      ],
    };
  }

  private analyzeFinancials(properties: any[], developmentPotential: any) {
    const acquisitionCost = properties.reduce((sum, p) => sum + (p.lastSalePrice || p.suburb.medianPrice || 0), 0);
    const developmentCost = developmentPotential.recommendedDwellings * 300000; // $300k per dwelling
    const totalInvestment = acquisitionCost + developmentCost;
    const estimatedGDV = developmentPotential.recommendedDwellings * (properties[0]?.suburb?.medianPrice || 800000);
    const profit = estimatedGDV - totalInvestment;
    const roi = (profit / totalInvestment) * 100;

    return {
      acquisitionCost,
      developmentCost,
      totalInvestment,
      estimatedGDV,
      profit,
      roi,
      irr: roi * 0.8, // Simplified IRR calculation
      paybackPeriod: totalInvestment / (profit / 3), // Assuming 3-year development
    };
  }

  private createAssemblyStrategy(properties: any[]) {
    const acquisitionOrder = properties.map((p, index) => ({
      propertyId: p.id,
      priority: index + 1,
      reasoning: index === 0 ? 'Key corner property' : 'Adjacent property for continuity',
      estimatedCost: p.lastSalePrice || p.suburb.medianPrice || 0,
    }));

    return {
      acquisitionOrder,
      totalTimeframe: 18, // months
      risks: [
        {
          type: 'Holdout Risk',
          probability: 'Medium',
          impact: 'High',
          mitigation: 'Offer premium for key properties',
        },
        {
          type: 'Market Risk',
          probability: 'Low',
          impact: 'Medium',
          mitigation: 'Monitor market conditions closely',
        },
      ],
    };
  }

  private compareWithIndividualDevelopment(properties: any[], developmentPotential: any) {
    const individualDevelopmentValue = properties.length * 1200000; // Assuming individual development value
    const amalgamatedDevelopmentValue = developmentPotential.recommendedDwellings * 800000;
    const valueUplift = amalgamatedDevelopmentValue - individualDevelopmentValue;
    const upliftPercentage = (valueUplift / individualDevelopmentValue) * 100;

    return {
      individualDevelopmentValue,
      amalgamatedDevelopmentValue,
      valueUplift,
      upliftPercentage,
    };
  }

  private calculateDevelopmentScore(properties: any[], landAnalysis: any): number {
    let score = 50;

    // Land size bonus
    if (landAnalysis.totalLandSize > 2000) score += 25;
    else if (landAnalysis.totalLandSize > 1500) score += 15;
    else if (landAnalysis.totalLandSize > 1000) score += 10;

    // Property count bonus
    if (properties.length >= 4) score += 15;
    else if (properties.length >= 3) score += 10;

    // Location bonus
    const avgMedianPrice = properties.reduce((sum, p) => sum + (p.suburb.medianPrice || 0), 0) / properties.length;
    if (avgMedianPrice > 1000000) score += 10;

    return Math.min(100, score);
  }
}
