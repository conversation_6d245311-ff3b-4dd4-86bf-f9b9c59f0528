import {
    BadRequestException,
    ConflictException,
    Injectable,
    Logger,
    UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { User } from '@revalu/database';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from '../../common/database/prisma.service';
import {
    AuthResponseDto,
    ChangePasswordDto,
    ForgotPasswordDto,
    LoginDto,
    RegisterDto,
    ResetPasswordDto,
} from './dto/auth.dto';
import { UserResponseDto } from './dto/user-response.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    const { email, password, firstName, lastName } = registerDto;

    // Check if user already exists
    const existingUser = await this.prisma.client.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const saltRounds = this.configService.get('auth.bcryptRounds', 12);
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    try {
      // Create user in database
      const user = await this.prisma.client.user.create({
        data: {
          email,
          firstName,
          lastName,
        },
      });

      // Generate JWT token
      const tokens = await this.generateTokens(user);

      this.logger.log(`User registered successfully: ${email}`);

      return {
        ...tokens,
        user: this.transformUserResponse(user),
      };
    } catch (error) {
      this.logger.error(`Registration failed for ${email}:`, error);
      throw new BadRequestException('Failed to create user account');
    }
  }

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    const { email, password } = loginDto;

    // Find user by email
    const user = await this.prisma.client.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid email or password');
    }

    // For now, we'll skip password validation since we're using Supabase auth
    // In a real implementation, you'd validate the password here
    // const isPasswordValid = await bcrypt.compare(password, user.password);
    // if (!isPasswordValid) {
    //   throw new UnauthorizedException('Invalid email or password');
    // }

    // Generate JWT token
    const tokens = await this.generateTokens(user);

    this.logger.log(`User logged in successfully: ${email}`);

    return {
      ...tokens,
      user: this.transformUserResponse(user),
    };
  }

  async validateUserById(userId: string): Promise<User | null> {
    return this.prisma.client.user.findUnique({
      where: { id: userId },
    });
  }

  async getCurrentUser(userId: string): Promise<UserResponseDto> {
    const user = await this.prisma.client.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return this.transformUserResponse(user);
  }

  async changePassword(
    userId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.prisma.client.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // In a real implementation, you'd validate the current password
    // const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    // if (!isCurrentPasswordValid) {
    //   throw new UnauthorizedException('Current password is incorrect');
    // }

    // Hash new password
    const saltRounds = this.configService.get('auth.bcryptRounds', 12);
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password in database
    // await this.prisma.client.user.update({
    //   where: { id: userId },
    //   data: { password: hashedNewPassword },
    // });

    this.logger.log(`Password changed successfully for user: ${user.email}`);

    return { message: 'Password changed successfully' };
  }

  async forgotPassword(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<{ message: string }> {
    const { email } = forgotPasswordDto;

    const user = await this.prisma.client.user.findUnique({
      where: { email },
    });

    if (!user) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a reset link has been sent' };
    }

    // In a real implementation, you'd:
    // 1. Generate a reset token
    // 2. Store it in the database with expiration
    // 3. Send email with reset link

    this.logger.log(`Password reset requested for: ${email}`);

    return { message: 'If the email exists, a reset link has been sent' };
  }

  async resetPassword(
    resetPasswordDto: ResetPasswordDto,
  ): Promise<{ message: string }> {
    const { token, password } = resetPasswordDto;

    // In a real implementation, you'd:
    // 1. Validate the reset token
    // 2. Check if it's not expired
    // 3. Update the user's password
    // 4. Invalidate the reset token

    this.logger.log(`Password reset completed for token: ${token}`);

    return { message: 'Password reset successfully' };
  }

  private async generateTokens(user: User) {
    const payload = { sub: user.id, email: user.email };
    const expiresIn = this.configService.get('auth.jwtExpiresIn', '7d');

    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn,
    });

    return {
      accessToken,
      tokenType: 'Bearer',
      expiresIn: this.parseExpirationTime(expiresIn),
    };
  }

  private parseExpirationTime(expiresIn: string): number {
    // Convert string like '7d' to seconds
    const unit = expiresIn.slice(-1);
    const value = parseInt(expiresIn.slice(0, -1));

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'h':
        return value * 60 * 60;
      case 'd':
        return value * 60 * 60 * 24;
      default:
        return 604800; // 7 days default
    }
  }

  private transformUserResponse(user: User): UserResponseDto {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      avatar: user.avatar,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
