import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiErrorResponse, ApiSuccessResponse } from '../../common/decorators/api-response.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
    DataSourceStatusDto,
    MarketDataWebhookDto,
    PropertyDataWebhookDto,
    WebhookResponseDto
} from './dto/webhook.dto';
import { WebhooksService } from './webhooks.service';

@ApiTags('webhooks')
@Controller('webhooks')
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('property-data')
  @Public()
  @ApiOperation({ 
    summary: 'Receive property data from external data pipeline',
    description: 'Webhook endpoint for receiving enriched property data from the external data pipeline. This endpoint processes property valuations, Event-Uplift factors, and intelligence scores.'
  })
  @ApiSuccessResponse(WebhookResponseDto, 'Property data processed successfully')
  @ApiErrorResponse(400, 'Invalid webhook payload')
  async receivePropertyData(
    @Body() dto: PropertyDataWebhookDto,
  ): Promise<WebhookResponseDto> {
    return this.webhooksService.processPropertyData(dto);
  }

  @Post('market-data')
  @Public()
  @ApiOperation({ 
    summary: 'Receive market data from external data pipeline',
    description: 'Webhook endpoint for receiving market trends, infrastructure updates, development applications, and other area-based data.'
  })
  @ApiSuccessResponse(WebhookResponseDto, 'Market data processed successfully')
  @ApiErrorResponse(400, 'Invalid webhook payload')
  async receiveMarketData(
    @Body() dto: MarketDataWebhookDto,
  ): Promise<WebhookResponseDto> {
    return this.webhooksService.processMarketData(dto);
  }

  @Get('data-sources/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: 'Get status of all data sources',
    description: 'Returns the current status, health, and performance metrics of all 18 data sources.'
  })
  @ApiPaginatedResponse(DataSourceStatusDto, 'Data source status retrieved successfully')
  async getDataSourceStatus(): Promise<DataSourceStatusDto[]> {
    return this.webhooksService.getDataSourceStatus();
  }
}
