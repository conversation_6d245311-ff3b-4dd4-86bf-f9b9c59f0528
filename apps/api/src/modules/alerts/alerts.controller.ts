import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiErrorResponse, ApiSuccessResponse } from '../../common/decorators/api-response.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AlertsService } from './alerts.service';
import {
    AlertHistoryDto,
    AlertHistoryResponseDto,
    AlertResponseDto,
    AlertSummaryDto,
    CreateAlertDto,
    TriggerAlertDto,
    UpdateAlertDto
} from './dto/alerts.dto';

@ApiTags('alerts')
@Controller('alerts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AlertsController {
  constructor(private readonly alertsService: AlertsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new alert' })
  @ApiSuccessResponse(AlertResponseDto, 'Alert created successfully')
  @ApiErrorResponse(400, 'Invalid alert parameters or property/suburb not found')
  async create(
    @CurrentUser('id') userId: string,
    @Body() dto: CreateAlertDto,
  ): Promise<AlertResponseDto> {
    return this.alertsService.create(userId, dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all alerts for current user' })
  @ApiPaginatedResponse(AlertResponseDto, 'Alerts retrieved successfully')
  async findAll(
    @CurrentUser('id') userId: string,
  ): Promise<AlertResponseDto[]> {
    return this.alertsService.findAllByUser(userId);
  }

  @Get('summary')
  @ApiOperation({ summary: 'Get alerts summary and statistics' })
  @ApiSuccessResponse(AlertSummaryDto, 'Alert summary retrieved successfully')
  async getSummary(
    @CurrentUser('id') userId: string,
  ): Promise<AlertSummaryDto> {
    return this.alertsService.getSummary(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific alert' })
  @ApiSuccessResponse(AlertResponseDto, 'Alert retrieved successfully')
  @ApiErrorResponse(404, 'Alert not found')
  async findOne(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
  ): Promise<AlertResponseDto> {
    return this.alertsService.findOne(id, userId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an alert' })
  @ApiSuccessResponse(AlertResponseDto, 'Alert updated successfully')
  @ApiErrorResponse(404, 'Alert not found')
  async update(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
    @Body() dto: UpdateAlertDto,
  ): Promise<AlertResponseDto> {
    return this.alertsService.update(id, userId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an alert' })
  @ApiSuccessResponse(Object, 'Alert deleted successfully')
  @ApiErrorResponse(404, 'Alert not found')
  async delete(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
  ): Promise<{ message: string }> {
    await this.alertsService.delete(id, userId);
    return { message: 'Alert deleted successfully' };
  }

  @Post('history')
  @ApiOperation({ summary: 'Get alert trigger history' })
  @ApiSuccessResponse(AlertHistoryResponseDto, 'Alert history retrieved successfully')
  @ApiErrorResponse(404, 'Alert not found')
  async getHistory(
    @Body() dto: AlertHistoryDto,
  ): Promise<AlertHistoryResponseDto> {
    return this.alertsService.getHistory(dto);
  }

  @Post('trigger')
  @ApiOperation({ summary: 'Manually trigger an alert (for testing)' })
  @ApiSuccessResponse(Object, 'Alert triggered successfully')
  @ApiErrorResponse(404, 'Alert not found')
  @ApiErrorResponse(400, 'Alert is not active')
  async trigger(
    @Body() dto: TriggerAlertDto,
  ): Promise<{ message: string }> {
    await this.alertsService.trigger(dto);
    return { message: 'Alert triggered successfully' };
  }
}
