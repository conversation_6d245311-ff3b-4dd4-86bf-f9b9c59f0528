import { PrismaClient, PropertyStatus, PropertyType, TrendDirection, TrendPeriod, ValuationMethod, ValuationStatus } from '@prisma/client';

const prisma = new PrismaClient();

// Sample property images from Unsplash
const PROPERTY_IMAGES = [
  'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
  'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800',
  'https://images.unsplash.com/photo-1605276374104-dee2a0ed3cd6?w=800',
  'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800',
  'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800',
  'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800',
  'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800',
  'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800',
  'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800',
  'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?w=800',
];

// Sample suburbs data
const SUBURBS_DATA = [
  {
    name: 'Toorak',
    state: 'VIC',
    postcode: '3142',
    country: 'Australia',
    latitude: -37.8403,
    longitude: 145.0075,
    population: 12817,
    area: 6.1,
    medianAge: 42.5,
    medianPrice: 2500000,
  },
  {
    name: 'Bondi',
    state: 'NSW',
    postcode: '2026',
    country: 'Australia',
    latitude: -33.8915,
    longitude: 151.2767,
    population: 11513,
    area: 2.6,
    medianAge: 35.2,
    medianPrice: 1800000,
  },
  {
    name: 'Paddington',
    state: 'QLD',
    postcode: '4064',
    country: 'Australia',
    latitude: -27.4598,
    longitude: 153.0078,
    population: 8956,
    area: 3.2,
    medianAge: 38.7,
    medianPrice: 950000,
  },
  {
    name: 'South Yarra',
    state: 'VIC',
    postcode: '3141',
    country: 'Australia',
    latitude: -37.8386,
    longitude: 144.9931,
    population: 25147,
    area: 3.9,
    medianAge: 32.1,
    medianPrice: 1200000,
  },
  {
    name: 'Surry Hills',
    state: 'NSW',
    postcode: '2010',
    country: 'Australia',
    latitude: -33.8886,
    longitude: 151.2094,
    population: 15144,
    area: 1.8,
    medianAge: 34.8,
    medianPrice: 1400000,
  },
  {
    name: 'Newstead',
    state: 'QLD',
    postcode: '4006',
    country: 'Australia',
    latitude: -27.4378,
    longitude: 153.0431,
    population: 6789,
    area: 2.1,
    medianAge: 31.5,
    medianPrice: 750000,
  },
  {
    name: 'Prahran',
    state: 'VIC',
    postcode: '3181',
    country: 'Australia',
    latitude: -37.8506,
    longitude: 144.9956,
    population: 12456,
    area: 2.8,
    medianAge: 33.9,
    medianPrice: 1100000,
  },
  {
    name: 'Potts Point',
    state: 'NSW',
    postcode: '2011',
    country: 'Australia',
    latitude: -33.8688,
    longitude: 151.2258,
    population: 7789,
    area: 0.9,
    medianAge: 36.2,
    medianPrice: 1600000,
  },
];

// Sample users data
const USERS_DATA = [
  {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
  },
  {
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
  },
  {
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Johnson',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
  },
  {
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Wilson',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
  },
];

// Property features pool
const PROPERTY_FEATURES = [
  'Pool', 'Garden', 'Garage', 'Fireplace', 'Balcony', 'Terrace',
  'Air Conditioning', 'Heating', 'Solar Panels', 'Security System',
  'Walk-in Closet', 'Study Room', 'Wine Cellar', 'Gym', 'Spa',
  'Ocean View', 'City View', 'Mountain View', 'Courtyard', 'Deck',
];

function getRandomElements<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomPrice(basePrice: number, variance: number = 0.3): number {
  const minPrice = basePrice * (1 - variance);
  const maxPrice = basePrice * (1 + variance);
  return Math.floor(Math.random() * (maxPrice - minPrice) + minPrice);
}

async function seedSuburbs() {
  console.log('🏘️  Seeding suburbs...');

  const suburbs = [];
  for (const suburbData of SUBURBS_DATA) {
    const suburb = await prisma.suburb.upsert({
      where: { name_state_postcode: { name: suburbData.name, state: suburbData.state, postcode: suburbData.postcode } },
      update: {},
      create: suburbData,
    });
    suburbs.push(suburb);
    console.log(`   ✅ Created suburb: ${suburb.name}, ${suburb.state}`);
  }

  return suburbs;
}

async function seedSchools(suburbs: any[]) {
  console.log('🏫 Seeding schools...');

  const schoolTypes = ['PRIMARY', 'SECONDARY', 'COMBINED'];
  const schoolNames = [
    'Melbourne Grammar School', 'Toorak Primary School', 'St Catherine\'s School',
    'Bondi Beach Public School', 'Waverley College', 'Rose Bay Secondary College',
    'Brisbane Grammar School', 'Paddington State School', 'St Joseph\'s College',
    'South Yarra Primary School', 'Melbourne High School', 'Prahran High School',
  ];

  for (let i = 0; i < suburbs.length; i++) {
    const suburb = suburbs[i];
    const schoolCount = getRandomNumber(1, 3);

    for (let j = 0; j < schoolCount; j++) {
      const school = await prisma.school.create({
        data: {
          name: schoolNames[i * 2 + j] || `${suburb.name} School ${j + 1}`,
          schoolType: schoolTypes[getRandomNumber(0, schoolTypes.length - 1)],
          suburbId: suburb.id,
          latitude: suburb.latitude + (Math.random() - 0.5) * 0.01,
          longitude: suburb.longitude + (Math.random() - 0.5) * 0.01,
          rating: Math.round((Math.random() * 4 + 6) * 10) / 10, // 6.0 to 10.0
        },
      });
      console.log(`   ✅ Created school: ${school.name}`);
    }
  }
}

async function seedTransportStops(suburbs: any[]) {
  console.log('🚇 Seeding transport stops...');

  const transportTypes = ['TRAIN', 'BUS', 'TRAM', 'FERRY'];
  const stopNames = [
    'Toorak Station', 'South Yarra Station', 'Prahran Station',
    'Bondi Junction', 'Edgecliff Station', 'Kings Cross Station',
    'Central Station', 'Roma Street Station', 'Fortitude Valley',
  ];

  for (let i = 0; i < suburbs.length; i++) {
    const suburb = suburbs[i];
    const stopCount = getRandomNumber(1, 4);

    for (let j = 0; j < stopCount; j++) {
      const transportType = transportTypes[getRandomNumber(0, transportTypes.length - 1)];
      const routes = Array.from({ length: getRandomNumber(1, 3) }, (_, k) =>
        `${transportType === 'TRAIN' ? 'Line' : 'Route'} ${getRandomNumber(1, 99)}`
      );

      const stop = await prisma.transportStop.create({
        data: {
          name: stopNames[i] || `${suburb.name} ${transportType} Stop`,
          transportType,
          suburbId: suburb.id,
          latitude: suburb.latitude + (Math.random() - 0.5) * 0.01,
          longitude: suburb.longitude + (Math.random() - 0.5) * 0.01,
          routes,
        },
      });
      console.log(`   ✅ Created transport stop: ${stop.name}`);
    }
  }
}

async function seedUsers() {
  console.log('👥 Seeding users...');

  const users = [];
  for (const userData of USERS_DATA) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: userData,
    });
    users.push(user);
    console.log(`   ✅ Created user: ${user.email}`);
  }

  return users;
}

async function seedProperties(suburbs: any[]) {
  console.log('🏠 Seeding properties...');

  const properties = [];
  const propertyTypes = Object.values(PropertyType);
  const propertyStatuses = Object.values(PropertyStatus);

  // Street names for realistic addresses
  const streetNames = [
    'Toorak Road', 'Chapel Street', 'High Street', 'Collins Street',
    'Campbell Parade', 'Oxford Street', 'King Street', 'Queen Street',
    'Given Terrace', 'Latrobe Terrace', 'Brunswick Street', 'Smith Street',
  ];

  for (let i = 0; i < 28; i++) {
    const suburb = suburbs[i % suburbs.length];
    const propertyType = propertyTypes[getRandomNumber(0, propertyTypes.length - 1)];
    const status = propertyStatuses[getRandomNumber(0, propertyStatuses.length - 1)];

    // Generate realistic property data based on type and suburb
    const bedrooms = propertyType === 'APARTMENT' ? getRandomNumber(1, 3) : getRandomNumber(2, 5);
    const bathrooms = Math.min(bedrooms, getRandomNumber(1, 3));
    const carSpaces = propertyType === 'APARTMENT' ? getRandomNumber(0, 2) : getRandomNumber(1, 3);

    const buildingSize = propertyType === 'APARTMENT'
      ? getRandomNumber(60, 200)
      : getRandomNumber(150, 400);

    const landSize = propertyType === 'APARTMENT'
      ? null
      : getRandomNumber(300, 1000);

    const yearBuilt = getRandomNumber(1920, 2023);
    const features = getRandomElements(PROPERTY_FEATURES, getRandomNumber(3, 8));
    const images = getRandomElements(PROPERTY_IMAGES, getRandomNumber(3, 6));

    const basePrice = suburb.medianPrice || 800000;
    const currentPrice = getRandomPrice(basePrice);
    const lastSalePrice = Math.floor(currentPrice * (0.7 + Math.random() * 0.2)); // 70-90% of current

    const property = await prisma.property.create({
      data: {
        address: `${getRandomNumber(1, 999)} ${streetNames[i % streetNames.length]}, ${suburb.name} ${suburb.state} ${suburb.postcode}`,
        suburbId: suburb.id,
        propertyType,
        status,
        bedrooms,
        bathrooms,
        carSpaces,
        landSize,
        buildingSize,
        yearBuilt,
        latitude: suburb.latitude + (Math.random() - 0.5) * 0.02,
        longitude: suburb.longitude + (Math.random() - 0.5) * 0.02,
        description: `Beautiful ${propertyType.toLowerCase()} in ${suburb.name} featuring ${features.slice(0, 3).join(', ')} and more.`,
        features,
        images,
        currentPrice,
        lastSalePrice,
        lastSaleDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 3), // Random date within last 3 years
      },
    });

    properties.push(property);
    console.log(`   ✅ Created property: ${property.address}`);
  }

  return properties;
}

async function seedValuations(properties: any[]) {
  console.log('💰 Seeding valuations...');

  const valuationMethods = Object.values(ValuationMethod);
  const valuationStatuses = Object.values(ValuationStatus);

  for (const property of properties) {
    // Create 1-3 valuations per property
    const valuationCount = getRandomNumber(1, 3);

    for (let i = 0; i < valuationCount; i++) {
      const method = valuationMethods[getRandomNumber(0, valuationMethods.length - 1)];
      const status = valuationStatuses[getRandomNumber(0, valuationStatuses.length - 1)];

      // Generate valuation value based on property's current price
      const baseValue = property.currentPrice || property.lastSalePrice || 800000;
      const estimatedValue = getRandomPrice(baseValue, 0.15); // ±15% variance

      const confidence = Math.round((Math.random() * 0.3 + 0.7) * 100) / 100; // 0.7 to 1.0

      const valuationDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000); // Random date within last year
      const expiryDate = new Date(valuationDate.getTime() + 180 * 24 * 60 * 60 * 1000); // 6 months from valuation

      const factors = {
        location: Math.round(Math.random() * 40 + 10) / 100, // 0.1 to 0.5
        size: Math.round(Math.random() * 30 + 15) / 100, // 0.15 to 0.45
        condition: Math.round(Math.random() * 25 + 10) / 100, // 0.1 to 0.35
        market: Math.round(Math.random() * 30 + 15) / 100, // 0.15 to 0.45
      };

      const valuation = await prisma.valuation.create({
        data: {
          propertyId: property.id,
          estimatedValue,
          confidence,
          method,
          status,
          valuationDate,
          expiryDate,
          factors,
          notes: `${method} valuation with ${Math.round(confidence * 100)}% confidence`,
        },
      });

      console.log(`   ✅ Created valuation: $${estimatedValue.toLocaleString()} for ${property.address.split(',')[0]}`);
    }
  }
}

async function seedMarketTrends(suburbs: any[]) {
  console.log('📈 Seeding market trends...');

  const trendDirections = Object.values(TrendDirection);
  const trendPeriods = Object.values(TrendPeriod);

  for (const suburb of suburbs) {
    // Create trends for the last 12 quarters
    for (let i = 0; i < 12; i++) {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - (i * 3 + 3)); // Go back 3 months each iteration

      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 3);

      const direction = trendDirections[getRandomNumber(0, trendDirections.length - 1)];
      const percentageChange = direction === 'UP'
        ? getRandomNumber(1, 15)
        : direction === 'DOWN'
        ? getRandomNumber(-15, -1)
        : getRandomNumber(-2, 2);

      const basePrice = suburb.medianPrice || 800000;
      const averagePrice = getRandomPrice(basePrice, 0.1);
      const medianPrice = Math.floor(averagePrice * (0.9 + Math.random() * 0.2));

      const trend = await prisma.marketTrend.create({
        data: {
          suburbId: suburb.id,
          period: 'QUARTERLY',
          direction,
          percentageChange,
          averagePrice,
          medianPrice,
          salesVolume: getRandomNumber(10, 100),
          daysOnMarket: getRandomNumber(20, 90),
          startDate,
          endDate,
        },
      });

      console.log(`   ✅ Created market trend: ${suburb.name} ${direction} ${percentageChange}%`);
    }
  }
}

async function seedSavedPropertiesAndSearches(users: any[], properties: any[]) {
  console.log('💾 Seeding saved properties and searches...');

  // Create saved properties for each user
  for (const user of users) {
    const savedCount = getRandomNumber(2, 6);
    const selectedProperties = getRandomElements(properties, savedCount);

    for (const property of selectedProperties) {
      const tags = getRandomElements(['investment', 'family-home', 'renovation', 'luxury', 'waterfront'], getRandomNumber(1, 3));

      await prisma.savedProperty.create({
        data: {
          userId: user.id,
          propertyId: property.id,
          notes: `Interested in this ${property.propertyType.toLowerCase()} - ${getRandomElements(['great location', 'good value', 'potential growth', 'nice features'], 1)[0]}`,
          tags,
        },
      });
    }

    // Create saved searches for each user
    const searchCount = getRandomNumber(1, 3);
    for (let i = 0; i < searchCount; i++) {
      const searchCriteria = {
        propertyType: getRandomElements(['HOUSE', 'APARTMENT'], 1),
        minPrice: getRandomNumber(500000, 1000000),
        maxPrice: getRandomNumber(1500000, 3000000),
        minBedrooms: getRandomNumber(2, 4),
        suburbs: getRandomElements(properties.map(p => p.suburbId), getRandomNumber(1, 3)),
      };

      await prisma.userSearch.create({
        data: {
          userId: user.id,
          query: `${searchCriteria.propertyType[0]} properties in selected suburbs`,
          filters: searchCriteria,
          resultsCount: getRandomNumber(5, 50),
        },
      });
    }

    console.log(`   ✅ Created saved properties and searches for ${user.email}`);
  }
}

async function main() {
  console.log('🌱 Starting comprehensive database seed...');

  try {
    // Seed in order due to dependencies
    const suburbs = await seedSuburbs();
    await seedSchools(suburbs);
    await seedTransportStops(suburbs);
    const users = await seedUsers();
    const properties = await seedProperties(suburbs);
    await seedValuations(properties);
    await seedMarketTrends(suburbs);
    await seedSavedPropertiesAndSearches(users, properties);

    console.log('');
    console.log('🎉 Database seed completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   • ${suburbs.length} suburbs`);
    console.log(`   • ${properties.length} properties`);
    console.log(`   • ${users.length} users`);
    console.log(`   • Schools and transport stops`);
    console.log(`   • Property valuations`);
    console.log(`   • Market trends`);
    console.log(`   • Saved properties and searches`);
    console.log('');
    console.log('🚀 Ready to explore your property intelligence platform!');

  } catch (error) {
    console.error('❌ Seed failed:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
