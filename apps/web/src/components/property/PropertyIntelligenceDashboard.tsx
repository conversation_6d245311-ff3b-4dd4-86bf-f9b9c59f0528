'use client';

import {
    Alert<PERSON>riangle,
    Building,
    CheckCircle,
    TrendingUp,
    Users,
    Zap
} from 'lucide-react';
import { useState } from 'react';

interface PropertyIntelligenceDashboardProps {
  property: {
    id: string;
    address: string;
    propertyType: string;
    bedrooms?: number;
    bathrooms?: number;
    carSpaces?: number;
    landSize?: number;
    buildingSize?: number;
    yearBuilt?: number;
    lastSalePrice?: number;
    lastSaleDate?: string;
    currentPrice?: number;
    images?: string[];
  };
}

export function PropertyIntelligenceDashboard({ property }: PropertyIntelligenceDashboardProps) {
  const [forecastPeriod, setForecastPeriod] = useState(12);

  // Mock intelligence data - in real app this would come from API
  const intelligence = {
    currentValue: 1275000,
    confidenceRange: { min: 1225000, max: 1325000 },
    monthlyChange: { amount: 25000, percentage: 2.0 },
    confidence: 87,
    dataSourcesActive: 16,
    totalDataSources: 18,
    
    scores: {
      investment: 85,
      development: 78,
      growth: 12.5,
      risk: 23,
      lifestyle: 88,
      marketPosition: 89
    },
    
    predictions: {
      12: { value: 1387500, confidence: 75 },
      24: { value: 1512500, confidence: 65 }
    },
    
    opportunities: [
      {
        type: 'Infrastructure',
        title: 'Light Rail Extension',
        description: 'New light rail station 800m away',
        impact: '+15-20%',
        timeline: 'Opening 2025',
        status: 'approved'
      },
      {
        type: 'Education',
        title: 'School Rating Improvement',
        description: 'Local school rating improved from 72 to 89',
        impact: 'High family demand',
        timeline: 'Current',
        status: 'active'
      },
      {
        type: 'Development',
        title: 'Duplex Potential',
        description: 'Similar approvals in area: 6 recent',
        impact: 'Value add: $400k+',
        timeline: '12-18 months',
        status: 'potential'
      }
    ],
    
    risks: [
      {
        type: 'Flood Risk',
        level: 'low',
        description: 'Property is 15m above flood zone'
      },
      {
        type: 'Market Risk',
        level: 'medium',
        description: 'Moderate volatility in local market'
      },
      {
        type: 'Climate Risk',
        level: 'low',
        description: 'Low bushfire and coastal erosion risk'
      }
    ],
    
    keyInsights: [
      'Property value has increased 8.5% in the last 12 months',
      'Located in top 10% of suburb for growth potential',
      'Strong rental demand with 1.2% vacancy rate',
      'Excellent transport connectivity to CBD'
    ]
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-8">
      {/* Hero Section - Real-Time Valuation */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{property.address}</h1>
            <p className="text-gray-600 mt-1">
              {property.propertyType} • {property.bedrooms} bed, {property.bathrooms} bath, {property.carSpaces} car
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600">Live</span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="text-center lg:text-left">
              <div className="text-5xl font-bold text-gray-900 mb-2">
                {formatCurrency(intelligence.currentValue)}
              </div>
              <div className="text-lg text-gray-600 mb-4">
                Range: {formatCurrency(intelligence.confidenceRange.min)} - {formatCurrency(intelligence.confidenceRange.max)}
              </div>
              <div className="flex items-center justify-center lg:justify-start space-x-4 mb-4">
                <div className="flex items-center text-green-600">
                  <TrendingUp className="w-5 h-5 mr-1" />
                  <span className="font-semibold">
                    +{formatCurrency(intelligence.monthlyChange.amount)} (+{intelligence.monthlyChange.percentage}%)
                  </span>
                </div>
                <span className="text-gray-500">since last month</span>
              </div>
              <div className="text-sm text-gray-600">
                Confidence: {intelligence.confidence}% | {intelligence.dataSourcesActive} of {intelligence.totalDataSources} data sources active
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Quick Stats</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Land Size:</span>
                  <span className="font-medium">{property.landSize}m²</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Building Size:</span>
                  <span className="font-medium">{property.buildingSize}m²</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Year Built:</span>
                  <span className="font-medium">{property.yearBuilt}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Sold:</span>
                  <span className="font-medium">{formatCurrency(property.lastSalePrice || 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Intelligence Scores Dashboard */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Intelligence Scores</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" strokeWidth="8" fill="none" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  stroke="#10b981" 
                  strokeWidth="8" 
                  fill="none"
                  strokeDasharray={`${intelligence.scores.investment * 2.51} 251`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">{intelligence.scores.investment}</span>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900">Investment Score</h3>
            <p className="text-sm text-gray-600">Market trends, infrastructure, quality</p>
          </div>

          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" strokeWidth="8" fill="none" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  stroke="#3b82f6" 
                  strokeWidth="8" 
                  fill="none"
                  strokeDasharray={`${intelligence.scores.development * 2.51} 251`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">{intelligence.scores.development}</span>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900">Development Score</h3>
            <p className="text-sm text-gray-600">Zoning, land size, DA activity</p>
          </div>

          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="40" stroke="#e5e7eb" strokeWidth="8" fill="none" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  stroke="#8b5cf6" 
                  strokeWidth="8" 
                  fill="none"
                  strokeDasharray={`${intelligence.scores.lifestyle * 2.51} 251`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xl font-bold text-gray-900">{intelligence.scores.lifestyle}</span>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900">Lifestyle Score</h3>
            <p className="text-sm text-gray-600">Schools, transport, amenities</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Growth Potential</span>
              <span className="text-lg font-bold text-green-600">+{intelligence.scores.growth}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{width: `${intelligence.scores.growth * 5}%`}}></div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Risk Score</span>
              <span className="text-lg font-bold text-green-600">{intelligence.scores.risk}/100</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{width: `${100 - intelligence.scores.risk}%`}}></div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Market Position</span>
              <span className="text-lg font-bold text-blue-600">{intelligence.scores.marketPosition}th %ile</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{width: `${intelligence.scores.marketPosition}%`}}></div>
            </div>
          </div>
        </div>
      </div>

      {/* AI Predictions & Insights */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">AI Predictions & Insights</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Value Forecast</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Forecast Period:</label>
                <select
                  value={forecastPeriod}
                  onChange={(e) => setForecastPeriod(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value={12}>12 months</option>
                  <option value={24}>24 months</option>
                </select>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {formatCurrency(intelligence.predictions[forecastPeriod as keyof typeof intelligence.predictions].value)}
                  </div>
                  <div className="text-sm text-gray-600 mb-4">
                    Predicted value in {forecastPeriod} months
                  </div>
                  <div className="flex items-center justify-center space-x-4">
                    <div className="flex items-center text-green-600">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      <span className="font-semibold">
                        +{((intelligence.predictions[forecastPeriod as keyof typeof intelligence.predictions].value / intelligence.currentValue - 1) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      Confidence: {intelligence.predictions[forecastPeriod as keyof typeof intelligence.predictions].confidence}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Insights</h3>
            <div className="space-y-3">
              {intelligence.keyInsights.map((insight, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{insight}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Opportunities Panel */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Opportunities</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {intelligence.opportunities.map((opportunity, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {opportunity.type === 'Infrastructure' && <Zap className="w-5 h-5 text-blue-500" />}
                  {opportunity.type === 'Education' && <Users className="w-5 h-5 text-green-500" />}
                  {opportunity.type === 'Development' && <Building className="w-5 h-5 text-purple-500" />}
                  <span className="text-sm font-medium text-gray-600">{opportunity.type}</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  opportunity.status === 'approved' ? 'bg-green-100 text-green-800' :
                  opportunity.status === 'active' ? 'bg-blue-100 text-blue-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {opportunity.status}
                </span>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2">{opportunity.title}</h3>
              <p className="text-gray-600 text-sm mb-4">{opportunity.description}</p>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Impact:</span>
                  <span className="font-medium text-green-600">{opportunity.impact}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Timeline:</span>
                  <span className="font-medium">{opportunity.timeline}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Assessment Panel */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Risk Assessment</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {intelligence.risks.map((risk, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">{risk.type}</h3>
                <div className="flex items-center space-x-2">
                  {risk.level === 'low' && <CheckCircle className="w-5 h-5 text-green-500" />}
                  {risk.level === 'medium' && <AlertTriangle className="w-5 h-5 text-yellow-500" />}
                  {risk.level === 'high' && <AlertTriangle className="w-5 h-5 text-red-500" />}
                  <span className={`text-sm font-medium capitalize ${getRiskColor(risk.level)}`}>
                    {risk.level}
                  </span>
                </div>
              </div>
              <p className="text-gray-600 text-sm">{risk.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
