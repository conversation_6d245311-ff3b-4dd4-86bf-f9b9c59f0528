import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@revalu/database';
import { 
  PropertyDataWebhookDto, 
  MarketDataWebhookDto, 
  WebhookResponseDto,
  DataSourceStatusDto,
  DataSourceType 
} from './dto/webhook.dto';

@Injectable()
export class WebhooksService {
  private readonly logger = new Logger(WebhooksService.name);

  constructor(private readonly prisma: PrismaService) {}

  async processPropertyData(dto: PropertyDataWebhookDto): Promise<WebhookResponseDto> {
    try {
      this.logger.log(`Processing property data from ${dto.source} for ${dto.propertyIdentifier}`);

      // Store raw webhook data
      const webhookRecord = await this.prisma.webhookData.create({
        data: {
          dataSourceId: await this.getOrCreateDataSource(dto.source, dto.sourceType),
          eventType: dto.eventType,
          payload: dto,
          processed: false,
        },
      });

      // Process the data based on event type
      let propertiesUpdated = 0;
      let alertsTriggered = 0;
      const errors: string[] = [];

      try {
        // Find or create property
        const property = await this.findOrCreateProperty(dto);
        
        if (property) {
          // Update property data
          await this.updatePropertyData(property.id, dto);
          propertiesUpdated++;

          // Update Event-Uplift factors if provided
          if (dto.data.upliftFactors) {
            await this.updateEventUpliftFactors(property.id, dto.data.upliftFactors);
          }

          // Update property intelligence if provided
          if (dto.data.investmentScore || dto.data.developmentScore) {
            await this.updatePropertyIntelligence(property.id, dto.data);
          }

          // Check for alert triggers
          alertsTriggered = await this.checkPropertyAlerts(property.id, dto);
        }

        // Mark webhook as processed
        await this.prisma.webhookData.update({
          where: { id: webhookRecord.id },
          data: { 
            processed: true, 
            processedAt: new Date(),
            propertyId: property?.id,
          },
        });

      } catch (error) {
        errors.push(error.message);
        this.logger.error(`Error processing property data: ${error.message}`);
      }

      return {
        status: errors.length === 0 ? 'success' : 'partial',
        message: `Processed property data for ${dto.propertyIdentifier}`,
        recordId: webhookRecord.id,
        details: {
          propertiesUpdated,
          alertsTriggered,
          errorsEncountered: errors,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to process property data: ${error.message}`);
      return {
        status: 'error',
        message: `Failed to process property data: ${error.message}`,
      };
    }
  }

  async processMarketData(dto: MarketDataWebhookDto): Promise<WebhookResponseDto> {
    try {
      this.logger.log(`Processing market data from ${dto.source} for ${dto.areaIdentifier}`);

      // Store raw webhook data
      const webhookRecord = await this.prisma.webhookData.create({
        data: {
          dataSourceId: await this.getOrCreateDataSource(dto.source, dto.sourceType),
          eventType: dto.eventType,
          payload: dto,
          processed: false,
        },
      });

      let suburbsUpdated = 0;
      let alertsTriggered = 0;
      const errors: string[] = [];

      try {
        // Find suburb
        const suburb = await this.findSuburb(dto.areaIdentifier);
        
        if (suburb) {
          // Update market trends
          if (dto.data.medianPrice || dto.data.priceChange) {
            await this.updateMarketTrends(suburb.id, dto);
            suburbsUpdated++;
          }

          // Update infrastructure projects
          if (dto.data.infrastructureProjects) {
            await this.updateInfrastructureProjects(suburb.id, dto.data.infrastructureProjects);
          }

          // Update development applications
          if (dto.data.developmentApplications) {
            await this.updateDevelopmentApplications(suburb.id, dto.data.developmentApplications);
          }

          // Update school ratings
          if (dto.data.schoolUpdates) {
            await this.updateSchoolRatings(suburb.id, dto.data.schoolUpdates);
          }

          // Update crime statistics
          if (dto.data.crimeStats) {
            await this.updateCrimeStatistics(suburb.id, dto.data.crimeStats);
          }

          // Check for market alerts
          alertsTriggered = await this.checkMarketAlerts(suburb.id, dto);

          // Mark webhook as processed
          await this.prisma.webhookData.update({
            where: { id: webhookRecord.id },
            data: { 
              processed: true, 
              processedAt: new Date(),
              suburbId: suburb.id,
            },
          });
        } else {
          errors.push(`Suburb not found: ${dto.areaIdentifier}`);
        }

      } catch (error) {
        errors.push(error.message);
        this.logger.error(`Error processing market data: ${error.message}`);
      }

      return {
        status: errors.length === 0 ? 'success' : 'partial',
        message: `Processed market data for ${dto.areaIdentifier}`,
        recordId: webhookRecord.id,
        details: {
          suburbsUpdated,
          alertsTriggered,
          errorsEncountered: errors,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to process market data: ${error.message}`);
      return {
        status: 'error',
        message: `Failed to process market data: ${error.message}`,
      };
    }
  }

  async getDataSourceStatus(): Promise<DataSourceStatusDto[]> {
    const dataSources = await this.prisma.dataSource.findMany({
      include: {
        webhookData: {
          where: {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        },
      },
    });

    return dataSources.map(source => {
      const recentRecords = source.webhookData.length;
      const errorRecords = source.webhookData.filter(w => !w.processed).length;
      const errorRate = recentRecords > 0 ? (errorRecords / recentRecords) * 100 : 0;

      return {
        name: source.name,
        type: source.type as DataSourceType,
        status: source.status as any,
        lastSync: source.lastSync,
        confidence: source.confidence,
        recentRecords,
        errorRate,
      };
    });
  }

  private async getOrCreateDataSource(name: string, type: DataSourceType): Promise<string> {
    let dataSource = await this.prisma.dataSource.findUnique({
      where: { name },
    });

    if (!dataSource) {
      dataSource = await this.prisma.dataSource.create({
        data: {
          name,
          type,
          status: 'ACTIVE',
        },
      });
    }

    // Update last sync
    await this.prisma.dataSource.update({
      where: { id: dataSource.id },
      data: { lastSync: new Date() },
    });

    return dataSource.id;
  }

  private async findOrCreateProperty(dto: PropertyDataWebhookDto) {
    // Try to find existing property by address
    let property = await this.prisma.property.findFirst({
      where: {
        address: { contains: dto.data.address || dto.propertyIdentifier },
      },
    });

    if (!property && dto.data.address) {
      // Create new property if it doesn't exist
      const suburb = await this.findOrCreateSuburb(dto.data.address);
      
      if (suburb) {
        property = await this.prisma.property.create({
          data: {
            address: dto.data.address,
            suburbId: suburb.id,
            propertyType: dto.data.propertyType as any || 'HOUSE',
            bedrooms: dto.data.bedrooms,
            bathrooms: dto.data.bathrooms,
            landSize: dto.data.landSize,
            buildingSize: dto.data.buildingSize,
            yearBuilt: dto.data.yearBuilt,
            latitude: dto.data.latitude,
            longitude: dto.data.longitude,
          },
        });
      }
    }

    return property;
  }

  private async findOrCreateSuburb(address: string) {
    // Simplified suburb extraction from address
    // In a real implementation, this would use a geocoding service
    const parts = address.split(',').map(p => p.trim());
    const suburbName = parts[parts.length - 3] || 'Unknown';
    const state = parts[parts.length - 2] || 'QLD';
    const postcode = parts[parts.length - 1] || '4000';

    let suburb = await this.prisma.suburb.findFirst({
      where: {
        name: suburbName,
        state: state,
        postcode: postcode,
      },
    });

    if (!suburb) {
      suburb = await this.prisma.suburb.create({
        data: {
          name: suburbName,
          state: state,
          postcode: postcode,
        },
      });
    }

    return suburb;
  }

  private async findSuburb(identifier: string) {
    // Try to find suburb by name, postcode, or ID
    return this.prisma.suburb.findFirst({
      where: {
        OR: [
          { name: { contains: identifier } },
          { postcode: identifier },
          { id: identifier },
        ],
      },
    });
  }

  private async updatePropertyData(propertyId: string, dto: PropertyDataWebhookDto) {
    const updateData: any = {};

    if (dto.data.estimatedValue) {
      // Create new valuation
      await this.prisma.valuation.create({
        data: {
          propertyId,
          estimatedValue: dto.data.estimatedValue,
          confidence: dto.data.confidence,
          method: dto.data.valuationMethod as any || 'AUTOMATED',
          status: 'COMPLETED',
        },
      });
    }

    if (dto.data.salePrice && dto.data.saleDate) {
      updateData.lastSalePrice = dto.data.salePrice;
      updateData.lastSaleDate = new Date(dto.data.saleDate);
    }

    if (dto.data.listingPrice) {
      updateData.currentPrice = dto.data.listingPrice;
    }

    if (Object.keys(updateData).length > 0) {
      await this.prisma.property.update({
        where: { id: propertyId },
        data: updateData,
      });
    }
  }

  private async updateEventUpliftFactors(propertyId: string, factors: any[]) {
    // Delete existing factors and create new ones
    await this.prisma.eventUpliftFactor.deleteMany({
      where: { propertyId },
    });

    for (const factor of factors) {
      await this.prisma.eventUpliftFactor.create({
        data: {
          propertyId,
          factorType: factor.type,
          factorName: factor.name,
          impact: factor.impact,
          confidence: factor.confidence,
          timeHorizon: factor.timeHorizon,
          source: 'webhook',
        },
      });
    }
  }

  private async updatePropertyIntelligence(propertyId: string, data: any) {
    await this.prisma.propertyIntelligence.upsert({
      where: { propertyId },
      update: {
        investmentScore: data.investmentScore,
        developmentScore: data.developmentScore,
        growthPotential: data.growthPotential,
        riskScore: data.riskScore,
        lastCalculated: new Date(),
      },
      create: {
        propertyId,
        investmentScore: data.investmentScore,
        developmentScore: data.developmentScore,
        growthPotential: data.growthPotential,
        riskScore: data.riskScore,
      },
    });
  }

  private async updateMarketTrends(suburbId: string, dto: MarketDataWebhookDto) {
    if (dto.data.medianPrice || dto.data.priceChange) {
      await this.prisma.marketTrend.create({
        data: {
          suburbId,
          period: 'MONTHLY',
          direction: dto.data.priceChange > 0 ? 'UP' : dto.data.priceChange < 0 ? 'DOWN' : 'STABLE',
          percentageChange: dto.data.priceChangePercent || 0,
          medianPrice: dto.data.medianPrice,
          averagePrice: dto.data.averagePrice,
          salesVolume: dto.data.salesVolume,
          daysOnMarket: dto.data.daysOnMarket,
          startDate: new Date(),
          endDate: new Date(),
        },
      });
    }
  }

  private async updateInfrastructureProjects(suburbId: string, projects: any[]) {
    for (const project of projects) {
      await this.prisma.infrastructureProject.upsert({
        where: { 
          id: `${suburbId}-${project.name.replace(/\s+/g, '-').toLowerCase()}` 
        },
        update: {
          status: project.status as any,
          budget: project.budget,
          startDate: project.startDate ? new Date(project.startDate) : null,
          endDate: project.endDate ? new Date(project.endDate) : null,
        },
        create: {
          id: `${suburbId}-${project.name.replace(/\s+/g, '-').toLowerCase()}`,
          name: project.name,
          type: project.type as any || 'TRANSPORT',
          status: project.status as any || 'PROPOSED',
          suburbId,
          budget: project.budget,
          startDate: project.startDate ? new Date(project.startDate) : null,
          endDate: project.endDate ? new Date(project.endDate) : null,
        },
      });
    }
  }

  private async updateDevelopmentApplications(suburbId: string, applications: any[]) {
    for (const app of applications) {
      await this.prisma.developmentApplication.upsert({
        where: { applicationId: app.applicationId },
        update: {
          status: app.status as any,
          estimatedValue: app.estimatedValue,
        },
        create: {
          applicationId: app.applicationId,
          suburbId,
          address: app.address,
          applicationType: app.type as any || 'DEVELOPMENT',
          status: app.status as any || 'SUBMITTED',
          submissionDate: new Date(app.submissionDate),
          estimatedValue: app.estimatedValue,
        },
      });
    }
  }

  private async updateSchoolRatings(suburbId: string, updates: any[]) {
    for (const update of updates) {
      const school = await this.prisma.school.findFirst({
        where: {
          suburbId,
          name: { contains: update.schoolName },
        },
      });

      if (school) {
        await this.prisma.school.update({
          where: { id: school.id },
          data: { rating: update.newRating },
        });
      }
    }
  }

  private async updateCrimeStatistics(suburbId: string, stats: any[]) {
    for (const stat of stats) {
      await this.prisma.crimeStatistic.create({
        data: {
          suburbId,
          crimeType: stat.type as any || 'OTHER',
          incidents: stat.incidents,
          period: stat.period,
          year: new Date().getFullYear(),
          rate: stat.rate,
        },
      });
    }
  }

  private async checkPropertyAlerts(propertyId: string, dto: PropertyDataWebhookDto): Promise<number> {
    // Find active alerts for this property
    const alerts = await this.prisma.alert.findMany({
      where: {
        propertyId,
        isActive: true,
      },
    });

    let triggeredCount = 0;

    for (const alert of alerts) {
      const shouldTrigger = this.evaluateAlertConditions(alert, dto.data);
      
      if (shouldTrigger) {
        await this.prisma.alertHistory.create({
          data: {
            alertId: alert.id,
            data: dto.data,
            notified: true,
            notifiedAt: new Date(),
          },
        });

        await this.prisma.alert.update({
          where: { id: alert.id },
          data: { lastTriggered: new Date() },
        });

        triggeredCount++;
      }
    }

    return triggeredCount;
  }

  private async checkMarketAlerts(suburbId: string, dto: MarketDataWebhookDto): Promise<number> {
    // Find active alerts for this suburb
    const alerts = await this.prisma.alert.findMany({
      where: {
        suburbId,
        isActive: true,
      },
    });

    let triggeredCount = 0;

    for (const alert of alerts) {
      const shouldTrigger = this.evaluateMarketAlertConditions(alert, dto.data);
      
      if (shouldTrigger) {
        await this.prisma.alertHistory.create({
          data: {
            alertId: alert.id,
            data: dto.data,
            notified: true,
            notifiedAt: new Date(),
          },
        });

        await this.prisma.alert.update({
          where: { id: alert.id },
          data: { lastTriggered: new Date() },
        });

        triggeredCount++;
      }
    }

    return triggeredCount;
  }

  private evaluateAlertConditions(alert: any, data: any): boolean {
    const conditions = alert.conditions;

    // Value change alerts
    if (alert.type === 'VALUE_CHANGE' && data.estimatedValue) {
      if (conditions.valueChangePercent) {
        // This would require comparing with previous value
        // Simplified for now
        return Math.abs(data.priceChangePercent || 0) >= conditions.valueChangePercent;
      }
    }

    // Price drop alerts
    if (alert.type === 'PRICE_DROP' && data.listingPrice) {
      if (conditions.priceDropPercent) {
        return (data.priceChangePercent || 0) <= -conditions.priceDropPercent;
      }
    }

    return false;
  }

  private evaluateMarketAlertConditions(alert: any, data: any): boolean {
    const conditions = alert.conditions;

    // Market trend alerts
    if (alert.type === 'MARKET_TREND') {
      if (conditions.marketChangePercent && data.priceChangePercent) {
        return Math.abs(data.priceChangePercent) >= conditions.marketChangePercent;
      }
    }

    // Development application alerts
    if (alert.type === 'DEVELOPMENT_APPLICATION' && data.developmentApplications) {
      return data.developmentApplications.length > 0;
    }

    // Infrastructure alerts
    if (alert.type === 'INFRASTRUCTURE_UPDATE' && data.infrastructureProjects) {
      return data.infrastructureProjects.length > 0;
    }

    return false;
  }
}
